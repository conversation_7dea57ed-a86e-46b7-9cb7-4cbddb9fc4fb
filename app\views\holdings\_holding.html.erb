<%# locals: (holding:) %>

<%= turbo_frame_tag dom_id(holding) do %>
  <div class="grid grid-cols-12 items-center text-primary text-sm font-medium p-4">
    <div class="col-span-4 flex items-center gap-4">
      <%= image_tag "https://logo.synthfinance.com/ticker/#{holding.ticker}", class: "w-9 h-9 rounded-full", loading: "lazy" %>

      <div class="space-y-0.5">
        <%= link_to holding.name, holding_path(holding), data: { turbo_frame: :drawer }, class: "hover:underline" %>

        <% if holding.amount %>
          <%= tag.p holding.ticker, class: "text-secondary text-xs uppercase" %>
        <% else %>
          <%= render "missing_price_tooltip" %>
        <% end %>
      </div>
    </div>

    <div class="col-span-2 flex justify-end items-center gap-2">
      <% if holding.weight %>
        <%= render "shared/progress_circle", progress: holding.weight %>
        <%= tag.p number_to_percentage(holding.weight, precision: 1) %>
      <% else %>
        <%= tag.p "--", class: "text-secondary mb-5" %>
      <% end %>
    </div>

    <div class="col-span-2 text-right">
      <%= tag.p format_money holding.avg_cost %>
      <%= tag.p t(".per_share"), class: "font-normal text-secondary" %>
    </div>

    <div class="col-span-2 text-right">
      <% if holding.amount_money %>
        <%= tag.p format_money holding.amount_money %>
      <% else %>
        <%= tag.p "--", class: "text-secondary" %>
      <% end %>
      <%= tag.p t(".shares", qty: number_with_precision(holding.qty, precision: 1)), class: "font-normal text-secondary" %>
    </div>

    <div class="col-span-2 text-right">
      <% if holding.trend %>
        <%= tag.p format_money(holding.trend.value), style: "color: #{holding.trend.color};" %>
        <%= tag.p "(#{number_to_percentage(holding.trend.percent, precision: 1)})", style: "color: #{holding.trend.color};" %>
      <% else %>
        <%= tag.p "--", class: "text-secondary mb-4" %>
      <% end %>
    </div>
  </div>
<% end %>
