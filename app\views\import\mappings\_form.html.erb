<%# locals: (mapping:) %>

<%= styled_form_with model: mapping,
                     scope: :import_mapping,
                     url: import_mapping_path(mapping.import, mapping),
                     class: "grid grid-cols-3 gap-2 items-center",
                     data: { controller: "auto-submit-form" },
                     html: { id: dom_id(mapping, :form) } do |form| %>
  <span><%= mapping.key.blank? ? "(unassigned)" : mapping.key %></span>

  <% if mapping.mappable_class.present? %>
    <%= form.hidden_field :mappable_type, value: mapping.mappable_class, id: dom_id(mapping, :mappable_type) %>
    <%= form.select :mappable_id,
                      mapping.selectable_values,
                      { container_class: mapping.invalid? ? "border-red-500" : nil, include_blank: mapping.requires_selection? ? "Select an option" : "Leave unassigned", selected: mapping.create_when_empty? ? mapping.class::CREATE_NEW_KEY : mapping.mappable_id },
                      "data-auto-submit-form-target": "auto", "data-autosubmit-trigger-event": "change", disabled: mapping.import.complete?, id: dom_id(mapping, :mappable_id) %>
  <% else %>
    <%= form.select :value, mapping.selectable_values,
                      { container_class: mapping.invalid? ? "border-red-500" : nil, include_blank: mapping.requires_selection? ? "Select an option" : "Leave unassigned" },
                      "data-auto-submit-form-target": "auto", "data-autosubmit-trigger-event": "change", disabled: mapping.import.complete?, id: dom_id(mapping, :value) %>
  <% end %>

  <%= form.hidden_field :key, value: mapping.key, id: dom_id(mapping, :key) %>
  <%= form.hidden_field :type, value: mapping.type, id: dom_id(mapping, :type) %>

  <span class="justify-self-end">
    <%= mapping.values_count %>
  </span>
<% end %>
