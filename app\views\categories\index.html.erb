<header class="flex items-center justify-between">
  <h1 class="text-primary text-xl font-medium"><%= t(".categories") %></h1>

  <div class="flex items-center gap-2">
    <%= render DS::Menu.new do |menu| %>
      <% menu.with_item(
          variant: "button",
          text: "Delete all",
          href: destroy_all_categories_path,
          method: :delete,
          icon: "trash-2",
          confirm: CustomConfirm.for_resource_deletion("all categories", high_severity: true)) %>
    <% end %>

    <%= render DS::Link.new(
      text: t(".new"),
      variant: "primary",
      icon: "plus",
      href: new_category_path,
      frame: :modal
    ) %>
  </div>
</header>

<div class="bg-container rounded-xl shadow-border-xs p-4">
  <% if @categories.any? %>
    <div class="space-y-4">
      <% if @categories.incomes.any? %>
        <%= render "categories/category_list_group", title: t(".categories_incomes"), categories: @categories.incomes %>
      <% end %>

      <% if @categories.expenses.any? %>
        <%= render "categories/category_list_group", title: t(".categories_expenses"), categories: @categories.expenses %>
      <% end %>
    </div>
  <% else %>
    <div class="flex justify-center items-center py-20">
      <div class="text-center flex flex-col items-center max-w-[500px]">
        <p class="text-sm text-secondary mb-4"><%= t(".empty") %></p>
        <div class="flex items-center gap-2">
          <%= render DS::Button.new(
            text: t(".bootstrap"),
            href: bootstrap_categories_path,
          ) %>

          <%= render DS::Link.new(
            text: t(".new"),
            variant: "outline",
            icon: "plus",
            href: new_category_path,
            frame: :modal
          ) %>
        </div>
      </div>
    </div>
  <% end %>
</div>
