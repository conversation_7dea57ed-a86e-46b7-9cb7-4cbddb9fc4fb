<%= render DS::Dialog.new(variant: :drawer) do |dialog| %>
  <% dialog.with_header do %>
    <div>
      <p class="text-sm text-secondary">Category</p>
      <h3 class="text-2xl font-medium text-primary">
        <%= @budget_category.name %>
      </h3>

      <% if @budget_category.budget.initialized? %>
        <p class="text-sm text-secondary">
          <span class="text-primary">
            <%= format_money(@budget_category.actual_spending_money) %>
          </span>
          <span>/</span>
          <span><%= format_money(@budget_category.budgeted_spending_money) %></span>
        </p>
      <% end %>
    </div>

    <% if @budget_category.budget.initialized? %>
      <div class="ml-auto w-10 h-10">
        <%= render "budget_categories/budget_category_donut",
                    budget_category: @budget_category %>
      </div>
    <% end %>
  <% end %>

  <% dialog.with_body do %>
    <% dialog.with_section(title: "Overview", open: true) do %>
      <div class="pb-4">
        <dl class="space-y-3 px-3 py-2">
          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary">
              <%= @budget_category.budget.start_date.strftime("%b %Y") %> spending
            </dt>
            <dd class="text-primary font-medium">
              <%= format_money @budget_category.actual_spending_money %>
            </dd>
          </div>

          <% if @budget_category.budget.initialized? %>
            <div class="flex items-center justify-between text-sm">
              <dt class="text-secondary">Status</dt>
              <% if @budget_category.available_to_spend.negative? %>
                <dd class="flex items-center gap-1 text-red-500 font-medium">
                  <%= icon "alert-circle", size: "sm", color: "destructive" %>
                  <%= format_money @budget_category.available_to_spend_money.abs %>
                  <span>overspent</span>
                </dd>
              <% elsif @budget_category.available_to_spend.zero? %>
                <dd class="flex items-center gap-1 text-orange-500 font-medium">
                  <%= icon "x-circle", size: "sm", color: "warning" %>
                  <%= format_money @budget_category.available_to_spend_money %>
                  <span>left</span>
                </dd>
              <% else %>
                <dd class="text-primary flex items-center gap-1 text-green-500 font-medium">
                  <%= icon "check-circle", size: "sm", color: "success" %>
                  <%= format_money @budget_category.available_to_spend_money %>
                  <span>left</span>
                </dd>
              <% end %>
            </div>

            <div class="flex items-center justify-between text-sm">
              <dt class="text-secondary">Budgeted</dt>
              <dd class="text-primary font-medium">
                <%= format_money @budget_category.budgeted_spending_money %>
              </dd>
            </div>
          <% end %>

          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary">Monthly average spending</dt>
            <dd class="text-primary font-medium">
              <%= @budget_category.avg_monthly_expense_money.format %>
            </dd>
          </div>

          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary">Monthly median spending</dt>
            <dd class="text-primary font-medium">
              <%= @budget_category.median_monthly_expense_money.format %>
            </dd>
          </div>
        </dl>
      </div>
    <% end %>

    <% dialog.with_section(title: "Recent Transactions", open: true) do %>
      <div class="space-y-2">
        <div class="px-3 py-4 space-y-2">
          <% if @recent_transactions.any? %>
            <ul class="space-y-2 mb-4">
              <% @recent_transactions.each_with_index do |transaction, index| %>
                <li class="flex gap-4 text-sm space-y-1">
                  <div class="flex flex-col items-center gap-1.5 pt-2">
                    <div class="rounded-full h-1.5 w-1.5 bg-gray-300"></div>
                    <% unless index == @recent_transactions.length - 1 %>
                      <div class="h-12 w-px bg-alpha-black-200"></div>
                    <% end %>
                  </div>

                  <div class="flex justify-between w-full">
                    <div>
                      <p class="text-secondary text-xs uppercase">
                        <%= transaction.entry.date.strftime("%b %d") %>
                      </p>
                      <%= link_to transaction.entry.name,
                                  transactions_path,
                                  class: "text-primary hover:underline",
                                  data: { turbo_frame: :_top } %>
                    </div>
                    <p class="text-primary font-medium">
                      <%= format_money transaction.entry.amount_money %>
                    </p>
                  </div>
                </li>
              <% end %>
            </ul>

            <%= render DS::Link.new(
              text: "View all category transactions",
              variant: "outline",
              full_width: true,
              href: transactions_path(q: {
                categories: [@budget_category.name],
                start_date: @budget.start_date,
                end_date: @budget.end_date
              }),
              frame: :_top
            ) %>
          <% else %>
            <p class="text-secondary text-sm mb-4">
              No transactions found for this budget period.
            </p>
          <% end %>
        </div>
      </div>
    <% end %>
  <% end %>
<% end %>
