<%# locals: (accounts:) %>

<% accounts.group_by(&:accountable_type).sort_by { |group, _| group }.each do |group, accounts| %>
  <div class="bg-container-inset p-1 rounded-xl">
    <div class="flex items-center px-4 py-2 text-xs font-medium text-secondary">
      <p><%= Accountable.from_type(group).display_name %></p>
      <span class="text-subdued mx-2">&middot;</span>
      <p><%= accounts.count %></p>

      <% unless accounts.any?(&:syncing?) %>
        <p class="ml-auto"><%= totals_by_currency(collection: accounts, money_method: :balance_money) %></p>
      <% end %>
    </div>
    <div class="bg-container rounded-lg shadow-border-xs">
      <% accounts.each_with_index do |account, index| %>
        <%= render account %>
        <% unless index == accounts.count - 1 %>
          <%= render "shared/ruler" %>
        <% end %>
      <% end %>
    </div>
  </div>
<% end %>
