<%# locals: (category:) %>

<div id="<%= dom_id(category) %>" class="flex justify-between items-center px-4 pb-4 <%= "pt-4" unless category.subcategory? %> <%= "pb-4" unless category.subcategories.any? %> bg-container">
  <div class="flex w-full items-center gap-2.5">
    <% if category.subcategory? %>
      <span style="color: <%= category.color %>">
        <%= icon "corner-down-right", size: "sm", color: "current", class: "ml-2" %>
      </span>
    <% end %>

    <%= render partial: "categories/badge", locals: { category: category } %>
  </div>

  <div class="justify-self-end">
    <%= render DS::Menu.new do |menu| %>
      <% menu.with_item(variant: "link", text: t(".edit"), icon: "pencil", href: edit_category_path(category), data: { turbo_frame: :modal }) %>

      <% if category.transactions.any? %>
        <% menu.with_item(variant: "link", text: t(".delete"), icon: "trash-2", href: new_category_deletion_path(category), destructive: true, data: { turbo_frame: :modal }) %>
      <% else %>
        <% menu.with_item(variant: "button", text: t(".delete"), icon: "trash-2", href: category_path(category), method: :delete) %>
      <% end %>
    <% end %>
  </div>
</div>
