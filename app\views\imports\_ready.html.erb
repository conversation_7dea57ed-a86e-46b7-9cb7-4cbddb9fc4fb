<%# locals: (import:) %>

<div class="text-center space-y-2 mb-4 mx-auto max-w-md">
  <h1 class="text-3xl text-primary font-medium"><%= t(".title") %></h1>
  <p class="text-secondary text-sm"><%= t(".description") %></p>
</div>

<div class="mx-auto max-w-2xl space-y-4">
  <div class="bg-container-inset rounded-xl p-1 space-y-1">
    <div class="flex justify-between items-center text-xs font-medium text-secondary uppercase px-5 py-3">
      <p>item</p>
      <p class="justify-self-end">count</p>
    </div>

    <div class="bg-container shadow-border-xs rounded-lg text-sm">
      <% import.dry_run.each do |key, count| %>
        <% resource = dry_run_resource(key) %>

        <div class="flex items-center justify-between gap-2 bg-container px-5 py-3 rounded-lg">
          <div class="flex items-center gap-3">
            <%= tag.div class: class_names(resource.bg_class, resource.text_class, "w-8 h-8 rounded-full flex justify-center items-center") do %>
              <%= icon resource.icon, color: "current" %>
            <% end %>

            <p><%= resource.label %></p>
          </div>

          <p class="justify-self-end"><%= count %></p>
        </div>

        <% if key != import.dry_run.keys.last %>
          <%= render "shared/ruler" %>
        <% end %>
      <% end %>
    </div>
  </div>

  <%= render DS::Button.new(text: "Publish import", href: publish_import_path(import), full_width: true) %>
</div>
