<%# locals: (category:, categories:) %>

<div data-controller="category" data-category-preset-colors-value="<%= Category::COLORS %>">
  <%= styled_form_with model: category, class: "space-y-4" do |f| %>
    <section class="space-y-4">
      <div class="w-fit mx-auto relative">
        <%= render partial: "color_avatar", locals: { category: category } %>

        <details data-category-target="details" data-action="mousedown->category#handleOutsideClick">
          <summary class="cursor-pointer absolute -bottom-2 -right-2 flex justify-center items-center bg-surface-inset hover:bg-surface-inset-hover border-2 w-7 h-7 border-subdued rounded-full text-secondary">
            <%= icon("pen", size: "sm") %>
          </summary>

          <div class="fixed right-0 sm:right-auto mx-2 sm:ml-8 sm:mr-0 mt-2 z-50 bg-container p-4 border border-alpha-black-25 rounded-2xl shadow-xs h-fit" data-category-target="popup">
            <div class="flex gap-2 flex-col mb-4" data-category-target="selection" style="<%= "display:none;" if @category.subcategory? %>">
              <div data-category-target="pickerSection"></div>
              <h4 class="text-gray-500 text-sm">Color</h4>
              <div class="flex flex-wrap md:flex-nowrap gap-2 items-center" data-category-target="colorsSection">
                <% Category::COLORS.each do |color| %>
                  <label class="relative">
                    <%= f.radio_button :color, color, class: "sr-only peer", data: { action: "change->category#handleColorChange" } %>
                    <div class="w-6 h-6 rounded-full cursor-pointer peer-checked:ring-2 peer-checked:ring-offset-2 peer-checked:ring-gray-500" style="background-color: <%= color %>"></div>
                  </label>
                <% end %>
                <label class="relative">
                  <%= f.radio_button :color, "custom-color", class: "sr-only peer", data: { category_target: "colorPickerRadioBtn"} %>
                  <div class="w-6 h-6 rounded-full cursor-pointer peer-checked:ring-2 peer-checked:ring-offset-2 peer-checked:ring-blue-500" data-category-target="pickerBtn" style="background: conic-gradient(red,orange,yellow,lime,green,teal,cyan,blue,indigo,purple,magenta,pink,red)"></div>
                </label>
              </div>
              <div class="flex gap-2 items-center hidden flex-col" data-category-target="paletteSection">
                <div class="flex gap-2 items-center w-full">
                  <div class="w-6 h-6 p-4 rounded-full cursor-pointer" style="background-color: <%= category.color %>" data-category-target="colorPreview"></div>
                  <%= f.text_field :color , data: { category_target: "colorInput"}, inline: true %>
                  <%= icon "palette", size: "2xl", data: { action: "click->category#toggleSections" } %>
                </div>
                <div data-category-target="validationMessage" class="hidden self-start flex gap-1 items-center text-xs text-destructive ">
                  <span>Poor contrast, choose darker color or</span>
                  <button type="button" class="underline cursor-pointer" data-action="category#autoAdjust">auto-adjust.</button>
                </div>
              </div>
            </div>

            <div class="flex flex-wrap gap-2 justify-center flex-col w-auto md:w-87">
              <h4 class="text-secondary text-sm">Icon</h4>
              <div class="flex flex-wrap gap-0.5">
                <% Category.icon_codes.each do |icon| %>
                  <label class="relative">
                    <%= f.radio_button :lucide_icon, icon, class: "sr-only peer", data: { action: "change->category#handleIconChange change->category#handleIconColorChange", category_target:"icon" } %>
                    <div class="text-secondary w-7 h-7 flex m-0.5 items-center justify-center rounded-full cursor-pointer hover:bg-container-inset-hover peer-checked:bg-container-inset border-1 border-transparent">
                      <%= icon(icon, size: "sm", color: "current") %>
                    </div>
                  </label>
                <% end %>
              </div>
            </div>
          </div>
        </details>
      </div>

      <% if category.errors.any? %>
        <%= render "shared/form_errors", model: category %>
      <% end %>

      <div class="space-y-2">
        <%= f.select :classification, [["Income", "income"], ["Expense", "expense"]], { label: "Classification" }, required: true %>
        <%= f.text_field :name, placeholder: t(".placeholder"), required: true, autofocus: true, label: "Name", data: { color_avatar_target: "name" } %>
        <% unless category.parent? %>
          <%= f.select :parent_id, categories.pluck(:name, :id), { include_blank: "(unassigned)", label: "Parent category (optional)" }, disabled: category.parent?, data: { action: "change->category#handleParentChange" } %>
        <% end %>
      </div>
    </section>

    <section>
      <%= hidden_field_tag :transaction_id, params[:transaction_id] %>
      <%= f.submit %>
    </section>
  <% end %>
</div>
