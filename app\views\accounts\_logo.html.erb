<%# locals: (account:, size: "md", color: nil) %>

<% size_classes = {
  "sm" => "w-6 h-6",
  "md" => "w-9 h-9",
  "lg" => "w-10 h-10",
  "full" => "w-full h-full"
} %>

<% if account.plaid_account_id? && account.institution_domain.present? %>
  <%= image_tag "https://logo.synthfinance.com/#{account.institution_domain}", class: "shrink-0 rounded-full #{size_classes[size]}" %>
<% elsif account.logo.attached? %>
  <%= image_tag account.logo, class: "shrink-0 rounded-full #{size_classes[size]}" %>
<% else %>
  <%= render DS::FilledIcon.new(variant: :text, hex_color: color || account.accountable.color, text: account.name, size: size, rounded: true) %>
<% end %>
