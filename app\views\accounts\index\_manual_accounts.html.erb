<%# locals: (accounts:) %>

<details open class="group bg-container p-4 shadow-border-xs rounded-xl">
  <summary class="flex items-center gap-2 focus-visible:outline-hidden">
    <%= icon("chevron-right", class: "group-open:transform group-open:rotate-90") %>

    <div class="flex items-center justify-center h-8 w-8 rounded-full bg-black/5">
      <%= icon("folder-pen") %>
    </div>

    <span class="mr-auto text-sm font-medium text-primary"><%= t(".other_accounts") %></span>
  </summary>

  <div class="space-y-4 mt-4">
    <%= render "accounts/index/account_groups", accounts: accounts %>
  </div>
</details>
