<% pending_session = Current.true_user.impersonated_support_sessions.pending.first %>
<% in_progress_session = Current.true_user.impersonated_support_sessions.in_progress.first %>

<div class="sticky top-0 left-0 w-full bg-black flex items-center justify-between font-mono">
  <div class="flex items-center bg-red-600 text-inverse px-6 py-4">
    <%= icon "alert-triangle", size: "lg", color: "current", class: "mr-2" %>
    <span class="text-inverse font-semibold uppercase">Access <%= in_progress_session.present? ? "Session" : "Request" %></span>
  </div>
  <div class="flex items-center space-x-2 px-2 py-2 text-white gap-4">
    <% if pending_session.present? %>
      <p class="text-xs max-w-3xl text-right">Maybe support staff has requested access to your account (likely to help you with a support request). If you approve the request, all activity they take will be logged for security and audit purposes.</p>
      <%= button_to "Approve", approve_impersonation_session_path(pending_session), method: :put, class: "inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-green-500" %>
      <%= button_to "Reject", reject_impersonation_session_path(pending_session), method: :put, class: "inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" %>
    <% elsif in_progress_session.present? %>
      <p class="text-xs max-w-3xl text-right">Someone from the Maybe Finance team is currently viewing your data.  You may end the session at any time.</p>
      <%= button_to "End Session", complete_impersonation_session_path(in_progress_session), method: :put, class: "inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
    <% else %>
      <p class="text-xs max-w-3xl text-right text-red-500">Something went wrong.  Please contact us.</p>
    <% end %>
  </div>
</div>
