---
name: Bug report
about: Open a bug report when you experience broken functionality within the latest
  version of the Maybe app
title: 'Bug: [Add descriptive title here]'
labels: ''
assignees: ''

---

## Before you start (required)

### General checklist

- [ ] I have removed personal / sensitive data from screenshots and logs
- [ ] I have searched [existing issues](https://github.com/maybe-finance/maybe/issues?q=is:issue) and [discussions](https://github.com/maybe-finance/maybe/discussions) to ensure this is not a duplicate issue
    
### How are you using Maybe?

- [ ] I am a paying Maybe customer (hosted version)
  - Paying Maybe users can also open requests in Intercom (if there is sensitive info involved)
- [ ] I am a self-hosted user

### Self hoster checklist

_Paying, hosted users should delete this entire section._

If you are a self-hosted user, please complete all of the information below.  Issues with incomplete information will be marked as `Needs Info` to help our small team prioritize bug fixes.

- Self hosted app commit SHA (find in user menu): [enter commit sha here]
  - [ ] I have confirmed that my app's commit is the latest version of Maybe
- Where are you hosting?
  - [ ] Render
  - [ ] Docker Compose
  - [ ] Umbrel
  - [ ] Other (please specify)

---

## Bug description

A clear and concise description of what the bug is.

### To Reproduce

Be as specific as possible so Maybe maintainers can quickly reproduce the bug you're experiencing.

Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

### Expected behavior

What is the intended behavior that you would expect?

### Screenshots and/or recordings

We highly recommend providing additional context with screenshots and/or screen recordings.  This will _significantly_ improve the chances of the bug being addressed and fixed quickly.
