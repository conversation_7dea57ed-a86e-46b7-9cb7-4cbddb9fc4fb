<%# locals: (category:) %>
<% category ||= Category.uncategorized %>

<div>
  <span class="flex items-center gap-1 text-sm font-medium rounded-full px-1.5 py-1 border truncate focus-visible:outline-none focus-visible:ring-0"
        style="
          background-color: color-mix(in oklab, <%= category.color %> 10%, transparent);
          border-color: color-mix(in oklab, <%= category.color %> 10%, transparent);
          color: <%= category.color %>;">
    <% if category.lucide_icon.present? %>
      <%= icon category.lucide_icon, size: "sm", color: "current" %>
    <% end %>
    <%= category.name %>
  </span>
</div>
