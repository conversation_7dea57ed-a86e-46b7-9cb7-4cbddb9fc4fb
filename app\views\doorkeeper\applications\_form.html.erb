<%= form_for application, url: doorkeeper_submit_path(application), as: :doorkeeper_application, html: { role: "form" } do |f| %>
  <% if application.errors.any? %>
    <div class="alert alert-danger" data-alert><p><%= t("doorkeeper.applications.form.error") %></p></div>
  <% end %>

  <div class="form-group row">
    <%= f.label :name, class: "col-sm-2 col-form-label font-weight-bold" %>
    <div class="col-sm-10">
      <%= f.text_field :name, class: "form-control #{ 'is-invalid' if application.errors[:name].present? }", required: true %>
      <%= doorkeeper_errors_for application, :name %>
    </div>
  </div>

  <div class="form-group row">
    <%= f.label :redirect_uri, class: "col-sm-2 col-form-label font-weight-bold" %>
    <div class="col-sm-10">
      <%= f.text_area :redirect_uri, class: "form-control #{ 'is-invalid' if application.errors[:redirect_uri].present? }" %>
      <%= doorkeeper_errors_for application, :redirect_uri %>
      <span class="form-text text-secondary">
        <%= t("doorkeeper.applications.help.redirect_uri") %>
      </span>

      <% if Doorkeeper.configuration.allow_blank_redirect_uri?(application) %>
        <span class="form-text text-secondary">
          <%= t("doorkeeper.applications.help.blank_redirect_uri") %>
        </span>
      <% end %>
    </div>
  </div>

  <div class="form-group row">
    <%= f.label :confidential, class: "col-sm-2 form-check-label font-weight-bold" %>
    <div class="col-sm-10">
      <%= f.check_box :confidential, class: "checkbox #{ 'is-invalid' if application.errors[:confidential].present? }" %>
      <%= doorkeeper_errors_for application, :confidential %>
      <span class="form-text text-secondary">
        <%= t("doorkeeper.applications.help.confidential") %>
      </span>
    </div>
  </div>

  <div class="form-group row">
    <%= f.label :scopes, class: "col-sm-2 col-form-label font-weight-bold" %>
    <div class="col-sm-10">
      <%= f.text_field :scopes, class: "form-control #{ 'has-error' if application.errors[:scopes].present? }" %>
      <%= doorkeeper_errors_for application, :scopes %>
      <span class="form-text text-secondary">
        <%= t("doorkeeper.applications.help.scopes") %>
      </span>
    </div>
  </div>

  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
      <%= f.submit t("doorkeeper.applications.buttons.submit"), class: "btn btn-primary" %>
      <%= link_to t("doorkeeper.applications.buttons.cancel"), oauth_applications_path, class: "btn btn-secondary" %>
    </div>
  </div>
<% end %>
