/* Variable font */
@font-face {
  font-family: 'Geist Mono';
  src: url('./geist_mono/GeistMono[wght].woff2') format('woff2-variations');
  font-weight: 100 950;
  font-style: normal;
  font-display: swap;
}

/* Static fonts (fallback) */
@supports not (font-variation-settings: normal) {
  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-Thin.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-UltraLight.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-Black.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Geist Mono';
    src: url('./geist_mono/GeistMono-UltraBlack.woff2') format('woff2');
    font-weight: 950;
    font-style: normal;
    font-display: swap;
  }
}
