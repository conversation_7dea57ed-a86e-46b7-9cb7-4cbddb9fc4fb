<% if params[:step] == "method_select" %>
  <%= render "accounts/new/method_selector",
             path: new_credit_card_path(return_to: params[:return_to]),
             show_us_link: @show_us_link,
             show_eu_link: @show_eu_link,
             accountable_type: "CreditCard" %>
<% else %>
  <%= render DS::Dialog.new do |dialog| %>
    <% dialog.with_header(title: t(".title")) %>
    <% dialog.with_body do %>
      <%= render "credit_cards/form", account: @account, url: credit_cards_path %>
    <% end %>
  <% end %>
<% end %>
