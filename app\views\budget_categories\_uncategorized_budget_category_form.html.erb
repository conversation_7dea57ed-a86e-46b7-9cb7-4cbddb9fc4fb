<%# locals: (budget:) %>

<% budget_category = budget.uncategorized_budget_category %>

<div id="<%= dom_id(budget, :uncategorized_budget_category_form) %>" class="flex gap-3">
  <div class="w-1 h-3 rounded-xl mt-1" style="background-color: <%= budget_category.category.color %>"></div>

  <div class="text-sm mr-3">
    <p class="text-primary font-medium mb-0.5"><%= budget_category.category.name %></p>
    <p class="text-secondary"><%= budget_category.avg_monthly_expense_money.format(precision: 0) %>/m avg</p>
  </div>

  <div class="ml-auto">
    <div class="form-field w-[120px]">
      <div class="flex items-center">
        <span class="text-subdued text-sm mr-2"><%= budget_category.budgeted_spending_money.currency.symbol %></span>
        <%= text_field_tag :uncategorized, budget_category.budgeted_spending_money.amount, autocomplete: "off", class: "form-field__input text-right [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none", disabled: true %>
      </div>
    </div>
  </div>
</div>
