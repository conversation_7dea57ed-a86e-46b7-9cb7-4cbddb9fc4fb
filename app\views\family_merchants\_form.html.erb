<%# locals: (family_merchant:) %>

<div data-controller="color-avatar">
  <%= styled_form_with model: family_merchant, class: "space-y-4" do |f| %>
    <section class="space-y-4">
      <% if family_merchant.errors.any? %>
        <%= render "shared/form_errors", model: family_merchant %>
      <% end %>

      <div class="w-fit m-auto mb-4">
        <%= render partial: "shared/color_avatar", locals: { name: family_merchant.name, color: family_merchant.color } %>
      </div>
      <div class="flex gap-2 items-center justify-center">
        <% FamilyMerchant::COLORS.each do |color| %>
          <label class="relative">
            <%= f.radio_button :color, color, class: "sr-only peer", data: { action: "change->color-avatar#handleColorChange" } %>
            <div class="w-6 h-6 rounded-full cursor-pointer peer-checked:ring-2 peer-checked:ring-offset-2 peer-checked:ring-blue-500" style="background-color: <%= color %>"></div>
          </label>
        <% end %>
      </div>
      <div class="relative flex items-center border border-secondary rounded-lg text-subdued">
        <%= f.text_field :name, placeholder: t(".name_placeholder"), autofocus: true, required: true, data: { color_avatar_target: "name" } %>
      </div>
    </section>

    <section>
      <%= f.submit %>
    </section>
  <% end %>
</div>
