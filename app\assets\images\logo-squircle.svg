<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_iii_4725_68011)">
<path d="M1.66199 28.3573C3.33915 4.37286 8.83917 -0.408237 32.8236 1.26892L41.868 1.90136C65.8524 3.57851 70.6335 9.07854 68.9563 33.063L68.3239 42.1073C66.6467 66.0917 61.1467 70.8728 37.1623 69.1957L28.1179 68.5632C4.13349 66.8861 -0.647606 61.3861 1.02955 37.4016L1.66199 28.3573Z" fill="url(#paint0_linear_4725_68011)"/>
<path d="M1.66199 28.3573C3.33915 4.37286 8.83917 -0.408237 32.8236 1.26892L41.868 1.90136C65.8524 3.57851 70.6335 9.07854 68.9563 33.063L68.3239 42.1073C66.6467 66.0917 61.1467 70.8728 37.1623 69.1957L28.1179 68.5632C4.13349 66.8861 -0.647606 61.3861 1.02955 37.4016L1.66199 28.3573Z" fill="black" fill-opacity="0.7"/>
</g>
<path d="M2.82179 28.4384C3.23922 22.4687 3.89051 17.7733 4.98031 14.1012C6.06625 10.4421 7.56711 7.86966 9.64032 6.06745C11.7135 4.26524 14.4698 3.13701 18.2445 2.57088C22.0324 2.00274 26.7729 2.01127 32.7425 2.42871L41.7868 3.06115C47.7565 3.47859 52.452 4.12988 56.124 5.21968C59.7831 6.30562 62.3556 7.80648 64.1578 9.87969C65.96 11.9529 67.0882 14.7092 67.6544 18.4838C68.2225 22.2718 68.214 27.0122 67.7965 32.9819L67.1641 42.0262C66.7466 47.9959 66.0953 52.6913 65.0056 56.3634C63.9196 60.0225 62.4188 62.5949 60.3455 64.3971C58.2723 66.1994 55.516 67.3276 51.7414 67.8937C47.9534 68.4619 43.213 68.4533 37.2434 68.0359L28.199 67.4034C22.2294 66.986 17.5339 66.3347 13.8619 65.2449C10.2028 64.159 7.6303 62.6581 5.82808 60.5849C4.02587 58.5117 2.89764 55.7554 2.33151 51.9808C1.76337 48.1928 1.77191 43.4524 2.18934 37.4827L2.82179 28.4384Z" stroke="white" stroke-width="2.32525"/>
<path d="M2.82179 28.4384C3.23922 22.4687 3.89051 17.7733 4.98031 14.1012C6.06625 10.4421 7.56711 7.86966 9.64032 6.06745C11.7135 4.26524 14.4698 3.13701 18.2445 2.57088C22.0324 2.00274 26.7729 2.01127 32.7425 2.42871L41.7868 3.06115C47.7565 3.47859 52.452 4.12988 56.124 5.21968C59.7831 6.30562 62.3556 7.80648 64.1578 9.87969C65.96 11.9529 67.0882 14.7092 67.6544 18.4838C68.2225 22.2718 68.214 27.0122 67.7965 32.9819L67.1641 42.0262C66.7466 47.9959 66.0953 52.6913 65.0056 56.3634C63.9196 60.0225 62.4188 62.5949 60.3455 64.3971C58.2723 66.1994 55.516 67.3276 51.7414 67.8937C47.9534 68.4619 43.213 68.4533 37.2434 68.0359L28.199 67.4034C22.2294 66.986 17.5339 66.3347 13.8619 65.2449C10.2028 64.159 7.6303 62.6581 5.82808 60.5849C4.02587 58.5117 2.89764 55.7554 2.33151 51.9808C1.76337 48.1928 1.77191 43.4524 2.18934 37.4827L2.82179 28.4384Z" stroke="url(#paint1_linear_4725_68011)" stroke-width="2.32525"/>
<path d="M3.66933 28.4976C4.08541 22.5474 4.73164 17.9253 5.79481 14.343C6.85131 10.7831 8.28392 8.3723 10.1977 6.70866C12.1115 5.04503 14.6982 3.96188 18.3705 3.4111C22.0659 2.85684 26.7329 2.86017 32.6832 3.27625L41.7276 3.9087C47.6779 4.32478 52.2999 4.97101 55.8823 6.03418C59.4422 7.09068 61.8529 8.52329 63.5166 10.4371C65.1802 12.3509 66.2634 14.9376 66.8141 18.6098C67.3684 22.3053 67.3651 26.9723 66.949 32.9226L66.3165 41.967C65.9005 47.9172 65.2542 52.5393 64.1911 56.1216C63.1345 59.6815 61.7019 62.0923 59.7882 63.7559C57.8744 65.4196 55.2877 66.5027 51.6154 67.0535C47.92 67.6078 43.2529 67.6044 37.3026 67.1883L28.2583 66.5559C22.308 66.1398 17.6859 65.4936 14.1036 64.4304C10.5437 63.3739 8.13293 61.9413 6.4693 60.0275C4.80566 58.1137 3.72251 55.527 3.17173 51.8548C2.61747 48.1593 2.6208 43.4923 3.03688 37.542L3.66933 28.4976Z" stroke="url(#paint2_linear_4725_68011)" stroke-opacity="0.05" stroke-width="4.02448"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.20106 14.4635C5.15118 18.0011 4.50747 22.5866 4.09206 28.5272L3.45962 37.5716C3.04421 43.5122 3.04348 48.1426 3.59081 51.7919C4.13394 55.4131 5.1946 57.9152 6.78912 59.7495C8.38363 61.5838 10.7138 62.9824 14.2242 64.0242C17.7617 65.0741 22.3472 65.7178 28.2878 66.1332L37.3322 66.7656C43.2728 67.181 47.9033 67.1818 51.5525 66.6344C55.1738 66.0913 57.6759 65.0306 59.5101 63.4361C61.3444 61.8416 62.743 59.5115 63.7848 56.0011C64.8347 52.4635 65.4784 47.878 65.8938 41.9374L66.5262 32.893C66.9417 26.9524 66.9424 22.322 66.3951 18.6727C65.8519 15.0515 64.7913 12.5494 63.1968 10.7151C61.6022 8.88082 59.2721 7.48225 55.7617 6.44043C52.2241 5.39055 47.6387 4.74684 41.698 4.33143L32.6537 3.69899C26.713 3.28358 22.0826 3.28284 18.4333 3.83018C14.8121 4.3733 12.31 5.43397 10.4757 7.02849C8.64145 8.623 7.24288 10.9531 6.20106 14.4635ZM32.8236 1.26892C8.83917 -0.408237 3.33915 4.37286 1.66199 28.3573L1.02955 37.4016C-0.647606 61.3861 4.13349 66.8861 28.1179 68.5632L37.1623 69.1957C61.1467 70.8728 66.6467 66.0917 68.3239 42.1073L68.9563 33.063C70.6335 9.07854 65.8524 3.57851 41.868 1.90136L32.8236 1.26892Z" fill="url(#paint3_linear_4725_68011)"/>
<g filter="url(#filter1_ddii_4725_68011)">
<path d="M20.8165 43.8927L14.5692 43.4559C13.0888 43.3523 11.8006 44.5292 11.6919 46.0845C11.5831 47.6398 12.695 48.9845 14.1753 49.088L20.4227 49.5248C21.903 49.6284 23.1912 48.4515 23.3 46.8962C23.4087 45.3409 22.2968 43.9962 20.8165 43.8927Z" fill="#F23E94"/>
<path d="M14.5574 43.6244L20.8047 44.0612C22.1842 44.1577 23.2343 45.4143 23.1315 46.8844C23.0287 48.3546 21.814 49.4528 20.4344 49.3564L14.1871 48.9195C12.8076 48.823 11.7576 47.5664 11.8604 46.0963C11.9632 44.6261 13.1779 43.5279 14.5574 43.6244Z" stroke="url(#paint4_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M48.3652 51.4775L54.6125 51.9144C56.0928 52.0179 57.381 50.841 57.4898 49.2857C57.5985 47.7305 56.4866 46.3858 55.0063 46.2823L48.759 45.8454C47.2787 45.7419 45.9905 46.9188 45.8817 48.474C45.7729 50.0293 46.8848 51.374 48.3652 51.4775Z" fill="#F23E94"/>
<path d="M54.6243 51.7459L48.3769 51.309C46.9974 51.2126 45.9474 49.956 46.0502 48.4858C46.153 47.0157 47.3677 45.9174 48.7472 46.0139L54.9945 46.4508C56.3741 46.5472 57.4241 47.8038 57.3213 49.274C57.2185 50.7441 56.0038 51.8423 54.6243 51.7459Z" stroke="url(#paint5_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M37.3154 45.0271L32.2298 44.6714C30.7495 44.5679 29.4613 45.7448 29.3525 47.3001C29.2438 48.8553 30.3556 50.2001 31.836 50.3036L36.9215 50.6592C38.4019 50.7627 39.6901 49.5858 39.7988 48.0306C39.9076 46.4753 38.7957 45.1306 37.3154 45.0271Z" fill="#F23E94"/>
<path d="M32.218 44.8399L37.3036 45.1956C38.6831 45.292 39.7331 46.5486 39.6303 48.0188C39.5275 49.4889 38.3128 50.5872 36.9333 50.4907L31.8478 50.1351C30.4682 50.0386 29.4182 48.782 29.521 47.3119C29.6238 45.8417 30.8385 44.7435 32.218 44.8399Z" stroke="url(#paint6_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M46.5039 43.2046L52.5198 43.6253C54.0001 43.7288 55.2884 42.5519 55.3971 40.9967C55.5059 39.4414 54.394 38.0967 52.9136 37.9932L46.8977 37.5725C45.4174 37.469 44.1292 38.6459 44.0204 40.2011C43.9116 41.7564 45.0235 43.1011 46.5039 43.2046Z" fill="#6927DA"/>
<path d="M52.5316 43.4568L46.5156 43.0361C45.1361 42.9397 44.0861 41.6831 44.1889 40.2129C44.2917 38.7428 45.5064 37.6445 46.8859 37.741L52.9019 38.1617C54.2814 38.2582 55.3314 39.5148 55.2286 40.9849C55.1258 42.455 53.9111 43.5533 52.5316 43.4568Z" stroke="url(#paint7_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M23.7094 35.95L17.6934 35.5293C16.2131 35.4258 14.9249 36.6027 14.8161 38.158C14.7074 39.7133 15.8193 41.058 17.2996 41.1615L23.3155 41.5822C24.7959 41.6857 26.0841 40.5088 26.1928 38.9535C26.3016 37.3983 25.1897 36.0535 23.7094 35.95Z" fill="#6927DA"/>
<path d="M17.6817 35.6978L23.6976 36.1185C25.0771 36.215 26.1272 37.4716 26.0244 38.9417C25.9215 40.4119 24.7069 41.5101 23.3273 41.4137L17.3114 40.993C15.9319 40.8965 14.8818 39.6399 14.9846 38.1698C15.0874 36.6996 16.3021 35.6014 17.6817 35.6978Z" stroke="url(#paint8_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M39.8134 37.0582L30.8613 36.4322C29.381 36.3287 28.0927 37.5055 27.984 39.0608C27.8752 40.6161 28.9871 41.9608 30.4675 42.0643L39.4195 42.6903C40.8999 42.7938 42.1881 41.6169 42.2968 40.0617C42.4056 38.5064 41.2937 37.1617 39.8134 37.0582Z" fill="#6927DA"/>
<path d="M30.8495 36.6007L39.8016 37.2267C41.1811 37.3231 42.2311 38.5797 42.1283 40.0499C42.0255 41.52 40.8108 42.6183 39.4313 42.5218L30.4792 41.8958C29.0997 41.7994 28.0497 40.5428 28.1525 39.0726C28.2553 37.6025 29.47 36.5042 30.8495 36.6007Z" stroke="url(#paint9_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M32.3636 28.1666L20.9406 27.3679C19.4603 27.2643 18.1721 28.4412 18.0633 29.9965C17.9546 31.5518 19.0665 32.8965 20.5468 33L31.9698 33.7988C33.4501 33.9023 34.7383 32.7254 34.8471 31.1701C34.9558 29.6148 33.8439 28.2701 32.3636 28.1666Z" fill="#1570EF"/>
<path d="M20.9289 27.5363L32.3518 28.3351C33.7314 28.4316 34.7814 29.6882 34.6786 31.1583C34.5758 32.6285 33.3611 33.7267 31.9816 33.6303L20.5586 32.8315C19.179 32.735 18.129 31.4784 18.2318 30.0083C18.3346 28.5381 19.5493 27.4399 20.9289 27.5363Z" stroke="url(#paint10_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M39.7417 34.3403L50.4352 35.0881C51.9156 35.1916 53.2038 34.0147 53.3125 32.4594C53.4213 30.9042 52.3094 29.5595 50.8291 29.456L40.1356 28.7082C38.6552 28.6047 37.367 29.7816 37.2583 31.3368C37.1495 32.8921 38.2614 34.2368 39.7417 34.3403Z" fill="#1570EF"/>
<path d="M50.447 34.9196L39.7535 34.1718C38.374 34.0754 37.324 32.8188 37.4268 31.3486C37.5296 29.8785 38.7442 28.7802 40.1238 28.8767L50.8173 29.6245C52.1968 29.7209 53.2468 30.9775 53.144 32.4477C53.0412 33.9178 51.8265 35.0161 50.447 34.9196Z" stroke="url(#paint11_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M48.8251 21.1728L42.7957 20.7512C41.3154 20.6476 40.0272 21.8245 39.9184 23.3798C39.8097 24.9351 40.9216 26.2798 42.4019 26.3833L48.4312 26.8049C49.9116 26.9084 51.1998 25.7315 51.3085 24.1763C51.4173 22.621 50.3054 21.2763 48.8251 21.1728Z" fill="#22CCEE"/>
<path d="M42.784 20.9196L48.8133 21.3413C50.1928 21.4377 51.2428 22.6943 51.14 24.1645C51.0372 25.6346 49.8226 26.7329 48.443 26.6364L42.4137 26.2148C41.0342 26.1183 39.9841 24.8617 40.0869 23.3916C40.1897 21.9214 41.4044 20.8232 42.784 20.9196Z" stroke="url(#paint12_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
<path d="M30.0984 19.8627L24.0691 19.4411C22.5887 19.3376 21.3005 20.5145 21.1918 22.0697C21.083 23.625 22.1949 24.9697 23.6752 25.0732L29.7046 25.4948C31.1849 25.5983 32.4731 24.4215 32.5819 22.8662C32.6906 21.3109 31.5787 19.9662 30.0984 19.8627Z" fill="#22CCEE"/>
<path d="M24.0573 19.6096L30.0866 20.0312C31.4661 20.1277 32.5162 21.3843 32.4134 22.8544C32.3106 24.3246 31.0959 25.4228 29.7163 25.3263L23.687 24.9047C22.3075 24.8083 21.2574 23.5517 21.3603 22.0815C21.4631 20.6114 22.6777 19.5131 24.0573 19.6096Z" stroke="url(#paint13_linear_4725_68011)" stroke-opacity="0.1" stroke-width="0.33782" stroke-linejoin="round"/>
</g>
<g opacity="0.23" filter="url(#filter2_f_4725_68011)">
<path d="M2.69258 40.4874L12.7122 44.9449L39.098 57.1213L66.9412 61.8859L45.5294 72.5984L-0.202764 68.4613L2.69258 40.4874Z" fill="#F24396"/>
</g>
<g opacity="0.23" filter="url(#filter3_f_4725_68011)">
<path d="M2.56821 1.97031L52.6272 -2.04293L69.5358 11.3492L56.9932 16.1074L23.2807 14.6892L2.56821 1.97031Z" fill="#22CCEE"/>
</g>
<defs>
<filter id="filter0_iii_4725_68011" x="0.72644" y="-2.61149" width="68.533" height="75.6876" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.27762"/>
<feGaussianBlur stdDeviation="1.59703"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.188235 0 0 0 0 0.219608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_4725_68011"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.57731"/>
<feGaussianBlur stdDeviation="2.68298"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.321569 0 0 0 0 0.905882 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_4725_68011" result="effect2_innerShadow_4725_68011"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.57731"/>
<feGaussianBlur stdDeviation="1.78866"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.933333 0 0 0 0 0.160784 0 0 0 0 0.509804 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_4725_68011" result="effect3_innerShadow_4725_68011"/>
</filter>
<filter id="filter1_ddii_4725_68011" x="1.54998" y="10.9893" width="66.0817" height="52.755" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.6891"/>
<feGaussianBlur stdDeviation="5.0673"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.29135 0 0 0 0 0.0895476 0 0 0 0 0.654593 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4725_68011"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.6891"/>
<feGaussianBlur stdDeviation="4.22275"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_4725_68011" result="effect2_dropShadow_4725_68011"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4725_68011" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.6891"/>
<feGaussianBlur stdDeviation="0.844549"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_4725_68011"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.6891"/>
<feGaussianBlur stdDeviation="0.844549"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_4725_68011" result="effect4_innerShadow_4725_68011"/>
</filter>
<filter id="filter2_f_4725_68011" x="-23.4553" y="17.2348" width="113.649" height="78.6161" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.6263" result="effect1_foregroundBlur_4725_68011"/>
</filter>
<filter id="filter3_f_4725_68011" x="-20.6843" y="-25.2955" width="113.473" height="64.6555" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.6263" result="effect1_foregroundBlur_4725_68011"/>
</filter>
<linearGradient id="paint0_linear_4725_68011" x1="53.7772" y1="8.36942" x2="38.7315" y2="35.4937" gradientUnits="userSpaceOnUse">
<stop stop-color="#363636"/>
<stop offset="1" stop-color="#141414"/>
</linearGradient>
<linearGradient id="paint1_linear_4725_68011" x1="37.3458" y1="1.58514" x2="32.6401" y2="68.8795" gradientUnits="userSpaceOnUse">
<stop stop-color="#52EDFF"/>
<stop offset="0.274483" stop-color="#4361EE"/>
<stop offset="0.629793" stop-color="#7209B7"/>
<stop offset="1" stop-color="#F12980"/>
</linearGradient>
<linearGradient id="paint2_linear_4725_68011" x1="37.019" y1="6.25836" x2="33.4897" y2="56.7291" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_4725_68011" x1="37.3458" y1="1.58514" x2="32.6401" y2="68.8795" gradientUnits="userSpaceOnUse">
<stop stop-color="#52EDFF"/>
<stop offset="0.274483" stop-color="#4361EE"/>
<stop offset="0.629793" stop-color="#7209B7"/>
<stop offset="1" stop-color="#F12980"/>
</linearGradient>
<linearGradient id="paint4_linear_4725_68011" x1="17.6928" y1="43.6743" x2="17.299" y2="49.3064" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_4725_68011" x1="51.4888" y1="51.6959" x2="51.8826" y2="46.0638" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_4725_68011" x1="34.7726" y1="44.8492" x2="34.3788" y2="50.4814" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_4725_68011" x1="49.5118" y1="43.415" x2="49.9057" y2="37.7829" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint8_linear_4725_68011" x1="20.7014" y1="35.7397" x2="20.3076" y2="41.3718" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_4725_68011" x1="35.3373" y1="36.7452" x2="34.9435" y2="42.3773" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_4725_68011" x1="26.6521" y1="27.7672" x2="26.2583" y2="33.3994" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_4725_68011" x1="45.0885" y1="34.7142" x2="45.4823" y2="29.0821" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint12_linear_4725_68011" x1="45.8104" y1="20.962" x2="45.4166" y2="26.5941" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint13_linear_4725_68011" x1="27.0837" y1="19.6519" x2="26.6899" y2="25.284" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
