<%# locals: (budget:) %>

<div class="flex flex-col gap-4 items-center justify-center h-full">
  <%= icon "alert-triangle", size: "lg", color: "destructive" %>
  <p class="text-secondary text-sm text-center">You have over-allocated your budget.  Please fix your allocations.</p>

  <%= render DS::Link.new(
    text: "Fix allocations",
    variant: "secondary",
    size: "sm",
    icon: "pencil",
    icon_position: "right",
    href: budget_budget_categories_path(budget)
  ) %>
</div>
