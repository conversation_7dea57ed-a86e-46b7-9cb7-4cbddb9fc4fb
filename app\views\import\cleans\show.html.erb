<%= content_for :header_nav do %>
  <%= render "imports/nav", import: @import %>
<% end %>

<%= content_for :previous_path, import_configuration_path(@import) %>

<div class="space-y-4 mx-auto max-w-5xl">
  <div class="text-center space-y-2 max-w-[400px] mx-auto mb-4">
    <h2 class="text-3xl text-primary font-medium"><%= t(".title") %></h2>
    <p class="text-secondary text-sm"><%= t(".description") %></p>
  </div>

  <% if @import.cleaned? %>
    <div class="bg-container border border-tertiary rounded-lg p-3 flex flex-col md:flex-row items-start md:items-center justify-between gap-2 md:gap-0">
      <div class="flex items-center gap-2">
        <%= icon "check-circle", size: "sm", color: "success" %>
        <p class="text-success text-sm">Your data has been cleaned</p>
      </div>

      <%= render DS::Link.new(
        text: "Next step",
        variant: "primary",
        href: import_confirm_path(@import),
        frame: :_top,
        class: "w-full md:w-auto"
      ) %>
    </div>
  <% else %>
    <div class="bg-container border border-tertiary rounded-lg p-3 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-0">
      <div class="flex items-center gap-2">
        <%= icon "alert-triangle", size: "sm", color: "destructive" %>
        <p class="text-destructive text-sm hidden md:block"><%= t(".errors_notice") %></p>
        <p class="text-destructive text-sm md:hidden"><%= t(".errors_notice_mobile") %></p>
      </div>

      <div class="flex justify-center w-full md:w-auto">
        <div class="bg-gray-50 rounded-lg inline-flex p-1 space-x-2 text-sm text-primary font-medium w-full md:w-auto">
          <%= link_to "All rows", import_clean_path(@import, per_page: params[:per_page], view: "all"), class: "p-2 rounded-lg flex-1 md:flex-auto text-center #{params[:view] != 'errors' ? 'bg-container' : ''}" %>
          <%= link_to "Error rows", import_clean_path(@import, per_page: params[:per_page], view: "errors"), class: "p-2 rounded-lg flex-1 md:flex-auto text-center #{params[:view] == 'errors' ? 'bg-container' : ''}" %>
        </div>
      </div>
    </div>
  <% end %>

  <div class="pb-12">
    <div class="bg-container-inset rounded-xl p-1 mb-6">
      <div class="overflow-x-auto">
        <div style="grid-template-columns: repeat(<%= @import.column_keys.count %>, minmax(150px, 1fr));" class="grid items-center uppercase text-xs font-medium text-secondary py-3">
          <% @import.column_keys.each do |key| %>
            <div class="px-5"><%= import_col_label(key) %></div>
          <% end %>
        </div>

        <div class="bg-container shadow-border-xs rounded-xl divide-y divide-alpha-black-200 theme-dark:divide-alpha-white-200">
          <% @rows.each do |row| %>
            <%= render "import/rows/form", row: row %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="fixed bottom-0 left-1/2 -translate-x-1/2 w-full p-12">
    <div class="shadow-border-xs rounded-lg p-3 max-w-2xl mx-auto bg-container">
      <%= render "shared/pagination", pagy: @pagy %>
    </div>
  </div>
</div>
