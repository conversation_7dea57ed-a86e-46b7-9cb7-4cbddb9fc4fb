<%# locals: (import:, mapping_class:, step_idx:) %>

<% mappings = mapping_class.for_import(import) %>
<% is_last_step = step_idx == import.mapping_steps.count - 1 %>

<div class="w-full max-w-full">
  <% if mapping_class == Import::AccountMapping && import.account.nil? %>
    <% if import.requires_account? %>
      <div class="w-full max-w-full overflow-hidden mb-4">
        <div class="overflow-x-auto">
          <div class="flex items-center justify-between p-4 gap-4 text-secondary bg-red-100 border border-red-200 rounded-lg w-[650px] min-w-0 mx-auto">
            <%= tag.p t(".no_accounts"), class: "text-sm" %>

            <%= render DS::Link.new(
              text: "Create account",
              variant: "primary",
              href: new_account_path(return_to: import_confirm_path(import)),
              frame: :modal
            ) %>
          </div>
        </div>
      </div>
    <% elsif import.has_unassigned_account? %>
      <div class="w-full max-w-full overflow-hidden mb-4">
        <div class="overflow-x-auto">
          <div class="flex items-center justify-between p-4 gap-4 text-secondary bg-yellow-100 border border-yellow-200 rounded-lg w-[650px] min-w-0 mx-auto">
            <%= tag.p t(".unassigned_account"), class: "text-sm" %>
            <%= render DS::Link.new(
              text: t(".create_account"),
              variant: "primary",
              href: new_account_path(return_to: import_confirm_path(import)),
              frame: :modal
            ) %>
          </div>
        </div>
      </div>
    <% end %>
  <% end %>

  <div class="space-y-4 w-full max-w-full">
    <div class="w-full max-w-full overflow-hidden">
      <div class="overflow-x-auto">
        <div class="bg-container-inset rounded-xl p-1 space-y-1 w-[650px] min-w-0 mx-auto">
          <div class="grid grid-cols-3 gap-2 text-xs font-medium text-secondary uppercase px-5 py-3">
            <p><%= t(".csv_mapping_label", mapping: mapping_label(mapping_class)) %></p>
            <p><%= t(".maybe_mapping_label", mapping: mapping_label(mapping_class)) %></p>
            <p class="justify-self-end"><%= t(".rows_label") %></p>
          </div>

          <div class="shadow-border-xs rounded-md divide-y divide-alpha-black-100 text-sm">
            <% mappings.sort_by(&:key).each do |mapping| %>
              <div class="px-5 py-3 bg-container first:rounded-tl-xl first:rounded-tr-xl last:rounded-bl-xl last:rounded-br-xl">
                <%= render partial: "import/mappings/form", locals: { mapping: mapping } %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-center w-full">
      <%= render DS::Link.new(
        text: "Next",
        variant: "primary",
        href: is_last_step ? import_path(import) : url_for(step: step_idx + 2),
        icon: "arrow-right",
        icon_position: "right",
        class: "w-full md:w-auto"
      ) %>
    </div>
  </div>
</div>
