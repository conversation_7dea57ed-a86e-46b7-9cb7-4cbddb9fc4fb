<div class="bg-container rounded-xl p-6 space-y-6">
  <div class="text-center space-y-2">
    <div class="mx-auto w-12 h-12 rounded-full bg-destructive-surface flex items-center justify-center mb-4">
      <%= icon("alert-circle", class: "w-6 h-6 text-destructive") %>
    </div>
    <h1 class="text-2xl font-medium text-primary"><%= t("doorkeeper.authorizations.error.title") %></h1>
  </div>

  <div class="bg-surface-inset rounded-lg p-4">
    <p class="text-sm text-secondary">
      <%= (local_assigns[:error_response] ? error_response : @pre_auth.error_response).body[:error_description] %>
    </p>
  </div>

  <div class="text-center">
    <%= render DS::Link.new(
      text: "Go back",
      href: "javascript:history.back()",
      variant: :secondary
    ) %>
  </div>
</div>
